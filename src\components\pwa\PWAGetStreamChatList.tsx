import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import {
  MessageSquare,
  RefreshCw,
  AlertCircle,
  WifiOff,
  Clock
} from 'lucide-react';
import { StreamChat } from 'stream-chat';
import { getStreamClient, connectUser } from '@/integrations/getstream/client';
import { isOnline, registerConnectivityListeners, isPWA } from '@/utils/pwa-utils';
import PWAMobileLayout from './PWAMobileLayout';
import { supabase } from '@/integrations/supabase/client';

interface ChatChannel {
  id: string;
  name: string;
  taskId?: string;
  taskTitle?: string;
  taskStatus?: string;
  lastMessage?: string;
  lastMessageTime?: string;
  unreadCount?: number;
  isTaskChat?: boolean;
}

const PWAGetStreamChatList: React.FC = () => {
  const navigate = useNavigate();
  const { user, profile } = useAuth();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [channels, setChannels] = useState<ChatChannel[]>([]);
  const [streamClient, setStreamClient] = useState<StreamChat | null>(null);
  const [offlineMode, setOfflineMode] = useState(!isOnline());

  // Effect to handle online/offline status
  useEffect(() => {
    const cleanup = registerConnectivityListeners(
      // Online callback
      () => {
        setOfflineMode(false);
        // Refresh data when coming back online
        if (streamClient) {
          fetchChannels(streamClient);
        }
      },
      // Offline callback
      () => {
        setOfflineMode(true);
      }
    );

    return cleanup;
  }, [streamClient]);

  // Initialize GetStream client
  useEffect(() => {
    if (!user) return;

    let connectionAttempts = 0;
    const maxAttempts = 3;
    let mounted = true;

    const initializeStreamClient = async () => {
      try {
        if (!mounted) return;
        setLoading(true);
        setError(null);

        // Get user display name
        const userName = profile?.first_name && profile?.last_name
          ? `${profile.first_name} ${profile.last_name}`
          : user.email?.split('@')[0] || 'User';

        console.log('[PWAGetStreamChatList] Connecting to GetStream...');

        // Connect to GetStream
        const client = await connectUser(
          user.id,
          userName,
          user.id, // This is just a placeholder, the actual token is generated server-side
          profile?.avatar_url || undefined
        );

        if (!mounted) return;

        // Verify the client is connected before proceeding
        if (!client.isConnected()) {
          console.warn('[PWAGetStreamChatList] Client not connected after connectUser call');

          // Wait a moment and check again
          await new Promise(resolve => setTimeout(resolve, 1000));

          if (!client.isConnected()) {
            throw new Error('Failed to establish connection to GetStream');
          }
        }

        console.log('[PWAGetStreamChatList] Successfully connected to GetStream');
        setStreamClient(client);

        // Fetch channels
        await fetchChannels(client);
      } catch (error) {
        console.error('[PWAGetStreamChatList] Error initializing Stream client:', error);

        if (!mounted) return;

        connectionAttempts++;
        if (connectionAttempts < maxAttempts) {
          console.log(`[PWAGetStreamChatList] Retrying connection (attempt ${connectionAttempts + 1}/${maxAttempts})...`);
          // Wait before retrying
          setTimeout(initializeStreamClient, 2000);
        } else {
          setError('Failed to load chats. Please try again later.');
          setLoading(false);
        }
      }
    };

    initializeStreamClient();

    return () => {
      mounted = false;

      // In PWA mode, we should NOT disconnect the user to prevent the
      // "Error: You can't use a channel after client.disconnect() was called" issue
      const isPWA = typeof window !== 'undefined' && window.matchMedia('(display-mode: standalone)').matches;

      if (!isPWA && streamClient) {
        console.log('[PWAGetStreamChatList] Cleaning up - disconnecting user');
        streamClient.disconnectUser();
      } else if (streamClient) {
        console.log('[PWAGetStreamChatList] Cleaning up - NOT disconnecting user in PWA mode');
      }
    };
  }, [user, profile]);

  // Fetch channels function
  const fetchChannels = async (client: StreamChat) => {
    try {
      // First, verify the client is connected
      if (!client.isConnected()) {
        console.warn('[PWAGetStreamChatList] Client not connected before fetching channels, attempting to reconnect');

        // Get user display name
        const userName = profile?.first_name && profile?.last_name
          ? `${profile.first_name} ${profile.last_name}`
          : user?.email?.split('@')[0] || 'User';

        // Try to reconnect
        await connectUser(
          user?.id || '',
          userName,
          user?.id || '', // This is just a placeholder, the actual token is generated server-side
          profile?.avatar_url || undefined
        );

        // Check again after reconnection attempt
        if (!client.isConnected()) {
          throw new Error('Client still not connected after reconnection attempt');
        }
      }

      console.log('[PWAGetStreamChatList] Fetching channels for user:', user?.id);

      // Get user's organization for security filtering
      const { data: userProfile } = await supabase
        .from('profiles')
        .select('organization_id, account_type')
        .eq('id', user.id)
        .single();

      // Fetch user's channels - use a more specific filter to ensure we get all task-related channels
      // We're looking for channels where:
      // 1. The user is a member
      // 2. The channel type is 'messaging'
      const filter = {
        type: 'messaging',
        members: { $in: [user?.id || ''] }
      };

      const sort = { last_message_at: -1 };

      const userChannels = await client.queryChannels(filter, sort, {
        limit: 50, // Increased limit to ensure we get all channels
        state: true,
        watch: true,
        message_limit: 10, // Limit messages to improve performance
      });

      // SECURITY: Filter channels based on organization membership (OPTIMIZED)
      const secureChannels = [];

      // Extract all task IDs from channels in one go
      const taskChannels = userChannels.filter(channel => channel.data?.task_id);
      const nonTaskChannels = userChannels.filter(channel => !channel.data?.task_id);

      // Add non-task channels (direct messages, etc.) immediately
      secureChannels.push(...nonTaskChannels);

      if (taskChannels.length > 0) {
        // Batch query all tasks at once instead of individual queries
        const taskIds = taskChannels.map(channel => channel.data.task_id);
        const { data: tasks, error: tasksError } = await supabase
          .from('tasks')
          .select('id, organization_id, visibility')
          .in('id', taskIds);

        if (tasksError) {
          console.error('[PWAGetStreamChatList] Error fetching tasks for security filtering:', tasksError);
          // If we can't validate, err on the side of caution and show no task channels
        } else if (tasks) {
          // Create a map for quick lookup
          const taskMap = new Map(tasks.map(task => [task.id, task]));

          // Process each channel with the pre-fetched task data
          for (const channel of taskChannels) {
            const taskId = channel.data.task_id;
            const task = taskMap.get(taskId);

            if (!task) {
              console.warn('[PWAGetStreamChatList] Task not found for channel:', channel.id);
              continue;
            }

            // Allow access if:
            // 1. User is in the same organization as the task
            // 2. User is a supplier and task is public
            const hasAccess = (
              userProfile?.organization_id === task.organization_id ||
              (userProfile?.account_type === 'supplier' && task.visibility === 'public')
            );

            if (hasAccess) {
              secureChannels.push(channel);
            } else {
              console.warn('[PWAGetStreamChatList] Filtering out unauthorized channel:', {
                channelId: channel.id,
                taskId,
                userOrg: userProfile?.organization_id,
                taskOrg: task.organization_id,
                userType: userProfile?.account_type,
                taskVisibility: task.visibility
              });

              // Note: We're not removing users from channels here to avoid rate limits
              // The removal will happen when they try to access the channel directly
            }
          }
        }
      }

      console.log('[PWAGetStreamChatList] Successfully fetched channels:', userChannels.length);
      console.log('[PWAGetStreamChatList] Secure channels after filtering:', secureChannels.length);

      // Debug: Log channel details to help troubleshoot
      secureChannels.forEach((channel, index) => {
        console.log(`[PWAGetStreamChatList] Secure Channel ${index + 1}:`, {
          id: channel.id,
          name: channel.data?.name,
          taskId: channel.data?.task_id,
          members: channel.state.members ? Object.keys(channel.state.members) : [],
          messageCount: channel.state.messages.length,
          lastMessageAt: channel.data?.last_message_at,
        });
      });

      // Get task details for channels that have task_id
      const taskIds = secureChannels
        .filter(channel => channel.data?.task_id)
        .map(channel => channel.data?.task_id);

      let taskDetails: Record<string, any> = {};

      if (taskIds.length > 0) {
        console.log('[PWAGetStreamChatList] Fetching task details for:', taskIds);

        try {
          const { data: tasksData, error: tasksError } = await supabase
            .from('tasks')
            .select('id, title, status')
            .in('id', taskIds);

          if (tasksError) {
            console.error('[PWAGetStreamChatList] Error fetching task details:', tasksError);
          } else if (tasksData) {
            // Create a map of task details by ID
            taskDetails = tasksData.reduce((acc, task) => {
              acc[task.id] = task;
              return acc;
            }, {} as Record<string, any>);

            console.log('[PWAGetStreamChatList] Fetched task details:', taskDetails);
          }
        } catch (taskFetchError) {
          console.error('[PWAGetStreamChatList] Error in task details fetch:', taskFetchError);
        }
      }

      // Format channels for display
      const formattedChannels = secureChannels.map(channel => {
        const taskId = channel.data?.task_id;
        const isTaskChat = !!taskId || channel.id.startsWith('task-');

        // Extract the task ID from the channel ID if it starts with 'task-'
        const extractedTaskId = isTaskChat && !taskId && channel.id.startsWith('task-')
          ? channel.id.replace('task-', '')
          : null;

        const finalTaskId = taskId || extractedTaskId;
        const task = finalTaskId ? taskDetails[finalTaskId] : null;

        // Determine the best title to use
        let displayTitle = 'Chat';

        // First priority: task title from database
        if (task?.title) {
          displayTitle = task.title;
        }
        // Second priority: task_title field in channel data (set by PWAGetStreamChatView)
        else if (channel.data?.task_title) {
          displayTitle = channel.data.task_title;
        }
        // Third priority: channel name
        else if (channel.data?.name && channel.data.name !== 'Chat' && channel.data.name !== 'Task Chat') {
          displayTitle = channel.data.name;
        }
        // Last resort: use a generic name based on channel type
        else if (isTaskChat) {
          displayTitle = 'Task Chat';
        }

        return {
          id: channel.id,
          name: displayTitle, // Use the determined title for the name field
          taskId: finalTaskId,
          taskTitle: displayTitle, // Use the same title for taskTitle
          taskStatus: task?.status,
          lastMessage: channel.state.messages[channel.state.messages.length - 1]?.text || 'No messages yet',
          lastMessageTime: channel.data?.last_message_at,
          unreadCount: channel.countUnread(),
          isTaskChat: isTaskChat
        };
      });

      // Sort channels: task chats first, then by last message time
      const sortedChannels = formattedChannels.sort((a, b) => {
        // First sort by whether it's a task chat
        if (a.isTaskChat && !b.isTaskChat) return -1;
        if (!a.isTaskChat && b.isTaskChat) return 1;

        // Then sort by last message time
        const timeA = a.lastMessageTime ? new Date(a.lastMessageTime).getTime() : 0;
        const timeB = b.lastMessageTime ? new Date(b.lastMessageTime).getTime() : 0;
        return timeB - timeA;
      });

      setChannels(sortedChannels);
      setLoading(false);
      setError(null); // Clear any previous errors
    } catch (error) {
      console.error('[PWAGetStreamChatList] Error fetching channels:', error);

      // Check if this is a connection error
      const errorMessage = error.toString();
      if (errorMessage.includes('connectUser') || errorMessage.includes('connection')) {
        setError('Connection issue. Please refresh the page or try again later.');
      } else {
        setError('Failed to load chats. Please try again later.');
      }

      setLoading(false);
    }
  };

  // Handle refresh
  const handleRefresh = async () => {
    if (!user) return;

    setLoading(true);
    setError(null);

    try {
      // If we don't have a stream client or it's not connected, reinitialize
      if (!streamClient || !streamClient.isConnected()) {
        console.log('[PWAGetStreamChatList] No connected client during refresh, reinitializing...');

        // Get user display name
        const userName = profile?.first_name && profile?.last_name
          ? `${profile.first_name} ${profile.last_name}`
          : user.email?.split('@')[0] || 'User';

        // Connect to GetStream
        const client = await connectUser(
          user.id,
          userName,
          user.id,
          profile?.avatar_url || undefined
        );

        setStreamClient(client);
        await fetchChannels(client);
      } else {
        // Use existing client
        await fetchChannels(streamClient);
      }
    } catch (error) {
      console.error('[PWAGetStreamChatList] Error refreshing channels:', error);
      setError('Failed to refresh. Please try again.');
      setLoading(false);
    }
  };

  // Get initials for avatar
  const getInitials = (name: string): string => {
    if (!name) return 'C';
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  // Format date
  const formatDate = (dateString?: string): string => {
    if (!dateString) return '';

    const date = new Date(dateString);
    const now = new Date();
    const diffDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));

    if (diffDays === 0) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffDays === 1) {
      return 'Yesterday';
    } else if (diffDays < 7) {
      return date.toLocaleDateString([], { weekday: 'short' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  // Handle channel click
  const handleChannelClick = (channel: ChatChannel) => {
    console.log('[PWAGetStreamChatList] Navigating to channel:', channel.id);
    navigate(`/messages/${channel.id}${channel.taskId ? `?task=${channel.taskId}` : ''}`);
  };

  return (
    <PWAMobileLayout>
      <div className="flex flex-col h-full">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b bg-white">
          <h1 className="text-xl font-semibold">Messages</h1>
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={loading || offlineMode}
          >
            <RefreshCw className={`h-4 w-4 mr-1 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>

        {/* Offline warning */}
        {offlineMode && (
          <div className="bg-yellow-50 p-2 text-center text-sm text-yellow-700 flex items-center justify-center">
            <WifiOff className="h-4 w-4 mr-2" />
            You're offline. Some features may be limited.
          </div>
        )}

        {/* Error state */}
        {error && (
          <div className="bg-red-50 border-b border-red-200 px-4 py-2 flex items-center">
            <AlertCircle className="h-4 w-4 text-red-500 mr-2" />
            <span className="text-sm text-red-700">{error}</span>
          </div>
        )}



        {/* Content */}
        <div className="flex-1 overflow-y-auto p-4">
          {loading ? (
            // Loading state
            <div className="rounded-lg overflow-hidden bg-white border">
              {Array(5).fill(0).map((_, i) => (
                <React.Fragment key={i}>
                  <div className="px-4 py-3">
                    <div className="flex items-center">
                      <Skeleton className="h-12 w-12 rounded-full mr-3 flex-shrink-0" />
                      <div className="flex-1">
                        <div className="flex justify-between items-baseline">
                          <Skeleton className="h-5 w-32" />
                          <Skeleton className="h-4 w-16 ml-2" />
                        </div>
                        <Skeleton className="h-4 w-48 mt-1" />
                      </div>
                    </div>
                  </div>
                  {i < 4 && (
                    <div className="h-px bg-gray-100 mx-4" />
                  )}
                </React.Fragment>
              ))}
            </div>
          ) : channels.length === 0 ? (
            // Empty state
            <div className="text-center py-12">
              <MessageSquare className="h-12 w-12 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-1">No messages yet</h3>
              <p className="text-gray-500">
                Your messages will appear here when you start chatting with others.
              </p>
            </div>
          ) : (
            // Channels list
            <div className="rounded-lg overflow-hidden bg-white border">
              {channels.map((channel, index) => (
                <React.Fragment key={channel.id}>
                  <div
                    className="px-4 py-3 cursor-pointer hover:bg-gray-50 transition-colors"
                    onClick={() => handleChannelClick(channel)}
                  >
                    <div className="flex items-center">
                      <Avatar className="h-12 w-12 mr-3 flex-shrink-0">
                        <AvatarFallback>{getInitials(channel.name)}</AvatarFallback>
                      </Avatar>
                      <div className="flex-1 min-w-0">
                        <div className="flex justify-between items-baseline">
                          <h3 className="font-medium text-gray-900 truncate">
                            {channel.isTaskChat ? channel.taskTitle : channel.name}
                          </h3>
                          <span className="text-xs text-gray-400 ml-2 flex-shrink-0">
                            {formatDate(channel.lastMessageTime)}
                          </span>
                        </div>
                        <p className="text-sm text-gray-500 truncate mt-1">{channel.lastMessage}</p>
                      </div>
                    </div>
                  </div>
                  {index < channels.length - 1 && (
                    <div className="h-px bg-gray-100 mx-4" />
                  )}
                </React.Fragment>
              ))}
            </div>
          )}
        </div>
      </div>
    </PWAMobileLayout>
  );
};

export default PWAGetStreamChatList;
