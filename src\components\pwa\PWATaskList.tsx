import React, { useState, useEffect, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import {
  PlusCircle,
  AlertCircle,
  SlidersHorizontal,
  Building,
  Globe,
  RefreshCw
} from 'lucide-react';
import PWATaskCard from './PWATaskCard';
import PWAComplianceList from './PWAComplianceList';
import { supabase } from '@/integrations/supabase/client';
import { Task } from '@/services/taskService';

import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import PWATaskStatusFilter from "./PWATaskStatusFilter";

// Define task status options
const STATUS_OPTIONS = [
  { value: 'all', label: 'All Tasks' },
  { value: 'open', label: 'Open' },
  { value: 'interest', label: 'Interest' },
  { value: 'questions', label: 'Questions' },
  { value: 'offer', label: 'Offers Received' },
  { value: 'assigned', label: 'Assigned' },
  { value: 'in_progress', label: 'In Progress' },
  { value: 'completed', label: 'Completed' },
  { value: 'confirmed', label: 'Confirmed' },
  { value: 'pending_payment', label: 'Payment Required' },
];

interface PWATaskListProps {
  showStatusFilter: boolean;
  setShowStatusFilter: (show: boolean) => void;
  taskTypeFilter: 'compliance' | 'internal' | 'external';
  setTaskTypeFilter: (filter: 'compliance' | 'internal' | 'external') => void;
  adminReviewCount: number;
  setAdminReviewCount: (count: number) => void;
  isAdminView: boolean;
  setIsAdminView: (isAdmin: boolean) => void;
}

const PWATaskList: React.FC<PWATaskListProps> = ({
  showStatusFilter,
  setShowStatusFilter,
  taskTypeFilter,
  setTaskTypeFilter,
  adminReviewCount,
  setAdminReviewCount,
  isAdminView,
  setIsAdminView
}) => {
  const { user, isAdmin } = useAuth();
  const navigate = useNavigate();
  const [tasks, setTasks] = useState<Task[]>([]);
  const [loading, setLoading] = useState(true);
  const [statusFilter, setStatusFilter] = useState('all');
  const [refreshing, setRefreshing] = useState(false);

  // Fetch tasks
  const fetchTasks = async () => {
    if (!user) return;

    try {
      setLoading(true);

      // Check if user is an admin
      if (isAdmin) {
        console.log('PWATaskList - User is admin, fetching all tasks');
        setIsAdminView(true);

        try {
          // Try to use the server-side API first
          const response = await fetch(`/api/admin-tasks?user_id=${user.id}`);

          if (response.ok) {
            const data = await response.json();
            const adminTasks = data.tasks;
            console.log('PWATaskList - Fetched admin tasks via server API:', adminTasks?.length || 0);

            if (adminTasks && adminTasks.length > 0) {
              // Sort by created_at
              adminTasks.sort((a: any, b: any) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());
              setTasks(adminTasks);
              return;
            }
          } else {
            throw new Error(`Server API returned ${response.status}`);
          }
        } catch (adminError) {
          console.error('PWATaskList - Error fetching admin tasks via server API:', adminError);

          // Fallback to direct Supabase query for all tasks
          try {
            console.log('PWATaskList - Falling back to direct Supabase query for all tasks');
            const { data: allTasks, error: allTasksError } = await supabase
              .from('tasks')
              .select('*')
              .order('created_at', { ascending: false });

            if (allTasksError) {
              console.error('PWATaskList - Error fetching all tasks:', allTasksError);
            } else if (allTasks && allTasks.length > 0) {
              console.log('PWATaskList - Fetched all tasks via direct query:', allTasks.length);
              setTasks(allTasks);
              return;
            }
          } catch (fallbackError) {
            console.error('PWATaskList - Error with fallback query:', fallbackError);
          }
        }
      }

      // If not admin or admin fetching failed, get user-specific tasks
      console.log('PWATaskList - Fetching user-specific tasks for user:', user.id);
      setIsAdminView(false);

      // First, get tasks created by or assigned to the user
      const { data: ownedOrAssignedTasks, error: ownedError } = await supabase
        .from('tasks')
        .select('*')
        .or(`user_id.eq.${user.id},assigned_to.eq.${user.id}`)
        .order('created_at', { ascending: false });

      // Then, get tasks where the user has submitted offers
      const { data: offerTasks, error: offerError } = await supabase
        .from('offers')
        .select('task_id')
        .eq('user_id', user.id);

      if (ownedError) {
        console.error('PWATaskList - Error fetching owned/assigned tasks:', ownedError);
      }

      if (offerError) {
        console.error('PWATaskList - Error fetching tasks with offers:', offerError);
      }

      // If we have tasks with offers, fetch those task details
      let tasksWithOffers: any[] = [];
      if (offerTasks && offerTasks.length > 0) {
        const taskIds = offerTasks.map(offer => offer.task_id);
        const { data: offerTaskDetails, error: detailsError } = await supabase
          .from('tasks')
          .select('*')
          .in('id', taskIds);

        if (detailsError) {
          console.error('PWATaskList - Error fetching task details for offers:', detailsError);
        } else {
          tasksWithOffers = offerTaskDetails || [];
        }
      }

      // Combine all tasks, removing duplicates
      const allTasks = [...(ownedOrAssignedTasks || []), ...tasksWithOffers];
      const uniqueTasks = Array.from(new Map(allTasks.map(task => [task.id, task])).values());

      // Sort by created_at
      uniqueTasks.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());

      console.log('PWATaskList - Fetched user-specific tasks:', uniqueTasks.length);

      setTasks(uniqueTasks || []);
    } catch (error) {
      console.error('PWATaskList - Error fetching tasks:', error);
    } finally {
      setLoading(false);
    }
  };

  // Refresh tasks
  const refreshTasks = async () => {
    setRefreshing(true);
    await fetchTasks();
    setRefreshing(false);
  };

  // Update admin review count when tasks change
  useEffect(() => {
    const count = tasks.filter(task =>
      task.status === 'open' &&
      task.visibility === 'admin' &&
      !task.assigned_to
    ).length;
    setAdminReviewCount(count);
  }, [tasks, setAdminReviewCount]);

  // Filter tasks based on selected status and task type
  const filteredTasks = useMemo(() => {
    let filtered = [...tasks];

    // Apply status filter
    if (statusFilter !== 'all') {
      if (statusFilter === 'admin_review') {
        // Admin review filter: tasks with 'open' status and 'admin' visibility
        // These are typically tasks created by teachers that need admin review
        filtered = filtered.filter(task =>
          task.status === 'open' &&
          task.visibility === 'admin' &&
          !task.assigned_to // Not yet assigned
        );
      } else {
        filtered = filtered.filter(task => task.status === statusFilter);
      }
    }

    // Apply task type filter (only for internal/external, compliance is handled separately)
    if (taskTypeFilter === 'internal') {
      filtered = filtered.filter(task => task.visibility === 'internal' || task.visibility === 'admin');
    } else if (taskTypeFilter === 'external') {
      filtered = filtered.filter(task => task.visibility === 'public');
    }

    return filtered;
  }, [tasks, statusFilter, taskTypeFilter]);

  // Initial fetch
  useEffect(() => {
    fetchTasks();
  }, [user]);

  return (
    <div className="flex flex-col h-full">
      {/* Header - filter moved to header action */}
      <div className="p-4 border-b">



        {/* Task type toggle */}
        <Tabs
          value={taskTypeFilter}
          onValueChange={(value) => setTaskTypeFilter(value as 'compliance' | 'internal' | 'external')}
          className="mb-4"
        >
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger
              value="compliance"
              className="flex items-center"
            >
              <AlertCircle className="h-4 w-4 mr-2" /> Compliance
            </TabsTrigger>
            <TabsTrigger
              value="internal"
              className="flex items-center"
            >
              <Building className="h-4 w-4 mr-2" /> Internal
            </TabsTrigger>
            <TabsTrigger
              value="external"
              className="flex items-center"
            >
              <Globe className="h-4 w-4 mr-2" /> External
            </TabsTrigger>
          </TabsList>
        </Tabs>

        {/* Status filter indicator - only show for task tabs, not compliance */}
        {taskTypeFilter !== 'compliance' && statusFilter !== 'all' && (
          <div className="mb-4 text-sm text-gray-500 flex items-center">
            <SlidersHorizontal className="h-3 w-3 mr-1" />
            Status filter: {' '}
            <span className="font-medium ml-1">
              {statusFilter === 'admin_review'
                ? 'Awaiting Admin Review'
                : STATUS_OPTIONS.find(option => option.value === statusFilter)?.label || statusFilter}
            </span>
          </div>
        )}
      </div>

      {/* Content area with tabs */}
      <div className="flex-1 overflow-hidden">
        <Tabs value={taskTypeFilter} className="h-full flex flex-col">
          {/* Compliance Tab Content */}
          <TabsContent value="compliance" className="flex-1 overflow-hidden mt-0">
            <PWAComplianceList />
          </TabsContent>

          {/* Internal Tasks Tab Content */}
          <TabsContent value="internal" className="flex-1 overflow-y-auto p-4 pb-20 mt-0">
            {loading ? (
              // Loading skeletons
              Array(5).fill(0).map((_, i) => (
                <div key={i} className="mb-2">
                  <Skeleton className="h-24 w-full rounded-md" />
                </div>
              ))
            ) : filteredTasks.length > 0 ? (
              // Task cards
              <div className="space-y-2">
                {filteredTasks.map(task => (
                  <PWATaskCard key={task.id} task={task} />
                ))}
              </div>
            ) : (
              // No tasks message
              <div className="flex flex-col items-center justify-center h-full text-center p-6">
                <Building className="h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium mb-2">No internal tasks found</h3>
                <p className="text-gray-500 mb-6">
                  {statusFilter === 'all'
                    ? "You don't have any internal tasks yet."
                    : `You don't have any ${statusFilter} internal tasks.`
                  }
                </p>
                <Button
                  onClick={() => navigate('/tasks/create')}
                  className="flex items-center"
                >
                  <PlusCircle className="h-4 w-4 mr-2" />
                  Create a Task
                </Button>
              </div>
            )}
          </TabsContent>

          {/* External Tasks Tab Content */}
          <TabsContent value="external" className="flex-1 overflow-y-auto p-4 pb-20 mt-0">
            {loading ? (
              // Loading skeletons
              Array(5).fill(0).map((_, i) => (
                <div key={i} className="mb-2">
                  <Skeleton className="h-24 w-full rounded-md" />
                </div>
              ))
            ) : filteredTasks.length > 0 ? (
              // Task cards
              <div className="space-y-2">
                {filteredTasks.map(task => (
                  <PWATaskCard key={task.id} task={task} />
                ))}
              </div>
            ) : (
              // No tasks message
              <div className="flex flex-col items-center justify-center h-full text-center p-6">
                <Globe className="h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium mb-2">No external tasks found</h3>
                <p className="text-gray-500 mb-6">
                  {statusFilter === 'all'
                    ? "You don't have any external tasks yet."
                    : `You don't have any ${statusFilter} external tasks.`
                  }
                </p>
                <Button
                  onClick={() => navigate('/tasks/create')}
                  className="flex items-center"
                >
                  <PlusCircle className="h-4 w-4 mr-2" />
                  Create a Task
                </Button>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>

      {/* Status Filter Sheet */}
      <PWATaskStatusFilter
        open={showStatusFilter}
        onOpenChange={setShowStatusFilter}
        statusFilter={statusFilter}
        setStatusFilter={setStatusFilter}
        isAdminView={true}
      />
    </div>
  );
};

export default PWATaskList;
