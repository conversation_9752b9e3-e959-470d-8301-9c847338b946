/**
 * GetStream Chat View Component for Mobile
 *
 * A mobile-optimized implementation of the chat view using GetStream
 */

import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, useSearchParams } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { ArrowLeft, Send, Info, WifiOff, AlertCircle } from 'lucide-react';
import { useGetStreamChat } from '@/hooks/use-getstream-chat';
import { Channel, MessageResponse } from 'stream-chat';
import {
  Chat,
  MessageInput,
  MessageList,
  Window,
} from 'stream-chat-react';

// Import GetStream CSS
import 'stream-chat-react/dist/css/v2/index.css';
import '@stream-io/stream-chat-css/dist/v2/css/index.css';

// Import custom CSS for mobile chat
import '@/styles/mobile-chat.css';

const GetStreamChatView: React.FC = () => {
  // Get URL parameters
  const { channelId } = useParams<{ channelId: string }>();
  const [searchParams] = useSearchParams();
  const taskId = searchParams.get('task');
  const navigate = useNavigate();
  const { user } = useAuth();

  // Local state
  const [taskTitle, setTaskTitle] = useState<string>('Chat');
  const [taskStatus, setTaskStatus] = useState<string>('');
  const [error, setError] = useState<string | null>(null);

  // Use the GetStream chat hook
  const {
    client,
    channel,
    isLoading,
    isSending,
    sendMessage,
    error: streamError
  } = useGetStreamChat({
    taskId: taskId || '',
    threadId: channelId
  });

  // Fetch task details if taskId is available
  useEffect(() => {
    if (!taskId) return;

    const fetchTaskDetails = async () => {
      try {
        if (channel) {
          setTaskTitle(channel.data?.name || 'Chat');
        }
      } catch (error) {
        console.error('[GetStreamChatView] Error fetching task details:', error);
      }
    };

    fetchTaskDetails();
  }, [taskId, channel]);

  // Update error state when streamError changes
  useEffect(() => {
    if (streamError) {
      setError(streamError.message);
    }
  }, [streamError]);

  // Custom input component
  const CustomInput = () => {
    const [text, setText] = useState('');

    const handleSubmit = async (e: React.FormEvent) => {
      e.preventDefault();
      if (!text.trim()) return;

      const result = await sendMessage(text);
      if (result.success) {
        setText('');
      } else {
        setError(result.reason || 'Failed to send message');
      }
    };

    return (
      <div className="mobile-message-input">
        <form onSubmit={handleSubmit} className="flex items-center p-2 border-t">
          <input
            type="text"
            value={text}
            onChange={(e) => setText(e.target.value)}
            placeholder="Type a message..."
            className="flex-1 p-2 border rounded-md focus:outline-none focus:ring-1 focus:ring-classtasker-blue"
            disabled={isSending}
          />
          <Button
            type="submit"
            size="icon"
            className="ml-2"
            disabled={!text.trim() || isSending}
          >
            <Send className="h-5 w-5" />
          </Button>
        </form>
      </div>
    );
  };

  return (
    <div className="flex flex-col h-screen bg-white">
      {/* Header */}
      <div className="sticky top-0 z-10 bg-white border-b border-gray-200 px-4 py-3 flex items-center justify-between">
        <div className="flex items-center">
          <button
            onClick={() => navigate('/mobile/chats')}
            className="mr-3 p-1 rounded-full hover:bg-gray-100"
          >
            <ArrowLeft size={20} />
          </button>
          <div>
            <h1 className="text-lg font-semibold">{taskTitle}</h1>
            {taskStatus && (
              <p className="text-xs text-gray-500">Status: {taskStatus}</p>
            )}
          </div>
        </div>
        {taskId && (
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigate(`/tasks/${taskId}`)}
          >
            View Task
          </Button>
        )}
      </div>

      {/* Error/Idle message */}
      {error && (
        <div className={`border-b px-4 py-2 flex items-center ${
          (error as any).isIdleTimeout
            ? 'bg-blue-50 border-blue-200'
            : 'bg-red-50 border-red-200'
        }`}>
          <AlertCircle className={`h-4 w-4 mr-2 ${
            (error as any).isIdleTimeout
              ? 'text-blue-500'
              : 'text-red-500'
          }`} />
          <span className={`text-sm ${
            (error as any).isIdleTimeout
              ? 'text-blue-700'
              : 'text-red-700'
          }`}>
            {error}
          </span>
        </div>
      )}

      {/* Chat content */}
      <div className="flex-1 overflow-hidden">
        {isLoading ? (
          // Loading state
          <div className="flex flex-col h-full justify-center items-center p-4">
            <Skeleton className="h-8 w-8 rounded-full mb-2" />
            <Skeleton className="h-4 w-32" />
          </div>
        ) : !client || !channel ? (
          // Error state
          <div className="flex flex-col h-full justify-center items-center p-4 text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mb-2" />
            <h3 className="text-lg font-medium text-gray-900 mb-1">Connection Error</h3>
            <p className="text-gray-500 mb-4">
              Could not connect to the chat. Please try again later.
            </p>
            <Button
              variant="outline"
              onClick={() => navigate('/mobile/chats')}
            >
              Back to Messages
            </Button>
          </div>
        ) : (
          // GetStream Chat Component
          <Chat client={client} theme="messaging light">
            <Channel channel={channel}>
              <Window>
                <MessageList />
                <CustomInput />
              </Window>
            </Channel>
          </Chat>
        )}
      </div>
    </div>
  );
};

export default GetStreamChatView;
