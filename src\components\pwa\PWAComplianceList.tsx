import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Input } from '@/components/ui/input';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Plus,
  Calendar,
  CheckCircle,
  AlertCircle,
  Clock,
  FileText,
  RefreshCw,
  Filter,
  Upload,
  X
} from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import { format, parseISO, isAfter, isBefore, addDays, addWeeks, addMonths, addYears } from 'date-fns';

// Define types
interface ComplianceTask {
  id: string;
  title: string;
  description: string;
  cycle: 'daily' | 'weekly' | 'monthly' | 'annually';
  last_completed_at: string | null;
  next_due_date: string;
  created_at: string;
  created_by: string;
  has_proof?: boolean;
  parent_task_id?: string | null;
  completion_id?: string | null;
  is_completed?: boolean;
}

interface ComplianceAttachment {
  id: string;
  compliance_task_id: string;
  file_name: string;
  file_type: string;
  file_path: string;
  uploaded_at: string;
  uploaded_by: string;
}

interface PWAComplianceListProps {
  filterStatus: 'all' | 'overdue' | 'pending' | 'completed';
  setFilterStatus: (filter: 'all' | 'overdue' | 'pending' | 'completed') => void;
}

const PWAComplianceList: React.FC<PWAComplianceListProps> = ({ filterStatus, setFilterStatus }) => {
  const { organizationId, user } = useAuth();
  const [complianceTasks, setComplianceTasks] = useState<ComplianceTask[]>([]);
  const [loading, setLoading] = useState(true);
  const [isProcessing, setIsProcessing] = useState(false);
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);
  const [selectedTask, setSelectedTask] = useState<ComplianceTask | null>(null);
  const [attachments, setAttachments] = useState<ComplianceAttachment[]>([]);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  // Calculate next due date based on cycle
  const calculateNextDueDate = (cycle: string): Date => {
    const now = new Date();
    switch (cycle) {
      case 'daily':
        return addDays(now, 1);
      case 'weekly':
        return addWeeks(now, 1);
      case 'monthly':
        return addMonths(now, 1);
      case 'annually':
        return addYears(now, 1);
      default:
        return addMonths(now, 1);
    }
  };

  // Fetch compliance tasks
  const fetchComplianceTasks = async () => {
    if (!organizationId) return;

    try {
      setLoading(true);

      // Fetch all compliance tasks
      const { data, error } = await supabase
        .from('compliance_tasks')
        .select('*')
        .eq('organization_id', organizationId)
        .order('next_due_date', { ascending: true });

      if (error) throw error;

      if (data) {
        const tasksWithProof = [];

        for (const task of data) {
          // Check for attachments (proof)
          const { data: attachments } = await supabase
            .from('compliance_attachments')
            .select('id')
            .eq('compliance_task_id', task.id);

          // Check for completion records
          const { data: completions } = await supabase
            .from('compliance_completions')
            .select('id')
            .eq('compliance_task_id', task.id)
            .order('completed_at', { ascending: false })
            .limit(1);

          // Add the task with proof and completion status
          tasksWithProof.push({
            ...task,
            has_proof: attachments && attachments.length > 0,
            is_completed: task.is_completed === true ? true :
              (task.last_completed_at ? isAfter(parseISO(task.last_completed_at), parseISO(task.next_due_date)) : false),
            completion_id: completions && completions.length > 0 ? completions[0].id : null
          });
        }

        setComplianceTasks(tasksWithProof);
      }
    } catch (error: any) {
      console.error('Error fetching compliance tasks:', error.message);
      toast({
        title: 'Error fetching compliance tasks',
        description: error.message,
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle marking a task as completed
  const handleMarkCompleted = async (task: ComplianceTask) => {
    try {
      setIsProcessing(true);

      const now = new Date().toISOString();
      const nextDueDate = calculateNextDueDate(task.cycle).toISOString();

      // Add completion record
      const { data: completionData, error: completionError } = await supabase
        .from('compliance_completions')
        .insert({
          compliance_task_id: task.id,
          completed_by: user?.id,
          completed_at: now,
        })
        .select()
        .single();

      if (completionError) throw completionError;

      // Update current task as completed
      const { error: updateError } = await supabase
        .from('compliance_tasks')
        .update({
          last_completed_at: now,
          is_completed: true,
          updated_at: now,
        })
        .eq('id', task.id);

      if (updateError) throw updateError;

      // Create new task for next cycle
      const { error: newTaskError } = await supabase
        .from('compliance_tasks')
        .insert({
          organization_id: task.organization_id,
          title: task.title,
          description: task.description,
          cycle: task.cycle,
          next_due_date: nextDueDate,
          created_by: user?.id,
          parent_task_id: task.id,
          created_at: now,
          updated_at: now,
        });

      if (newTaskError) throw newTaskError;

      toast({
        title: 'Success',
        description: 'Task completed and next cycle created',
      });

      // Refresh tasks
      fetchComplianceTasks();
    } catch (error: any) {
      console.error('Error completing task:', error.message);
      toast({
        title: 'Error completing task',
        description: error.message,
        variant: 'destructive',
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // Fetch attachments for a specific task
  const fetchAttachments = async (taskId: string) => {
    try {
      const { data, error } = await supabase
        .from('compliance_attachments')
        .select('*')
        .eq('compliance_task_id', taskId)
        .order('uploaded_at', { ascending: false });

      if (error) throw error;

      if (data) {
        setAttachments(data as ComplianceAttachment[]);
      }
    } catch (error: any) {
      console.error('Error fetching attachments:', error.message);
      toast({
        title: 'Error fetching attachments',
        description: error.message,
        variant: 'destructive',
      });
    }
  };

  // Handle file upload
  const handleFileUpload = async () => {
    if (!selectedFile || !selectedTask || !user) return;

    try {
      setIsProcessing(true);

      // Upload file to storage
      const fileExt = selectedFile.name.split('.').pop();
      const fileName = `${Math.random().toString(36).substring(2, 15)}.${fileExt}`;
      const filePath = `compliance/${selectedTask.id}/${fileName}`;

      const { error: uploadError } = await supabase.storage
        .from('attachments')
        .upload(filePath, selectedFile);

      if (uploadError) throw uploadError;

      // Add record to compliance_attachments table
      const { error: dbError } = await supabase
        .from('compliance_attachments')
        .insert({
          compliance_task_id: selectedTask.id,
          file_name: selectedFile.name,
          file_type: selectedFile.type,
          file_path: filePath,
          uploaded_by: user.id,
        });

      if (dbError) throw dbError;

      toast({
        title: 'Success',
        description: 'Proof uploaded successfully',
      });

      // Reset and close dialog
      setSelectedFile(null);
      setUploadDialogOpen(false);

      // Refresh tasks and attachments
      fetchComplianceTasks();
      fetchAttachments(selectedTask.id);
    } catch (error: any) {
      console.error('Error uploading file:', error.message);
      toast({
        title: 'Error uploading file',
        description: error.message,
        variant: 'destructive',
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // Get task status
  const getTaskStatus = (task: ComplianceTask) => {
    if (task.is_completed) return 'completed';

    const now = new Date();
    const dueDate = parseISO(task.next_due_date);

    if (isBefore(dueDate, now)) return 'overdue';
    return 'pending';
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge className="bg-green-100 text-green-800 border-green-200">Completed</Badge>;
      case 'overdue':
        return <Badge className="bg-red-100 text-red-800 border-red-200">Overdue</Badge>;
      default:
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">Pending</Badge>;
    }
  };

  // Get cycle badge
  const getCycleBadge = (cycle: string) => {
    const colors = {
      daily: 'bg-blue-100 text-blue-800 border-blue-200',
      weekly: 'bg-purple-100 text-purple-800 border-purple-200',
      monthly: 'bg-orange-100 text-orange-800 border-orange-200',
      annually: 'bg-gray-100 text-gray-800 border-gray-200'
    };

    return (
      <Badge className={colors[cycle as keyof typeof colors] || colors.monthly}>
        {cycle.charAt(0).toUpperCase() + cycle.slice(1)}
      </Badge>
    );
  };

  // Filter and sort tasks by importance (overdue -> pending -> completed)
  const filteredTasks = complianceTasks
    .filter(task => {
      const status = getTaskStatus(task);
      if (filterStatus === 'all') return true;
      return status === filterStatus;
    })
    .sort((a, b) => {
      const statusA = getTaskStatus(a);
      const statusB = getTaskStatus(b);

      // Define priority order: overdue (0), pending (1), completed (2)
      const getPriority = (status: string) => {
        switch (status) {
          case 'overdue': return 0;
          case 'pending': return 1;
          case 'completed': return 2;
          default: return 1;
        }
      };

      const priorityA = getPriority(statusA);
      const priorityB = getPriority(statusB);

      // Sort by priority first
      if (priorityA !== priorityB) {
        return priorityA - priorityB;
      }

      // Within same priority, sort by due date (earliest first for overdue/pending, latest first for completed)
      const dueDateA = new Date(a.next_due_date).getTime();
      const dueDateB = new Date(b.next_due_date).getTime();

      if (statusA === 'completed') {
        // For completed tasks, show most recently completed first
        return dueDateB - dueDateA;
      } else {
        // For overdue/pending, show earliest due date first (most urgent)
        return dueDateA - dueDateB;
      }
    });

  // Initial fetch
  useEffect(() => {
    if (organizationId) {
      fetchComplianceTasks();
    }
  }, [organizationId]);

  return (
    <div className="flex flex-col h-full bg-gray-50">
      {/* Content - filter moved to header */}
      <div className="flex-1 overflow-y-auto">
        {loading ? (
          // Loading state
          <div className="p-4 space-y-3">
            {Array(5).fill(0).map((_, i) => (
              <div key={i} className="bg-white rounded-lg p-4 border">
                <div className="flex items-start space-x-3">
                  <Skeleton className="h-5 w-5 rounded" />
                  <div className="flex-1 space-y-2">
                    <Skeleton className="h-5 w-3/4" />
                    <div className="flex gap-2">
                      <Skeleton className="h-6 w-16" />
                      <Skeleton className="h-6 w-20" />
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : filteredTasks.length === 0 ? (
          // Empty state
          <div className="flex flex-col items-center justify-center h-full text-gray-500 p-8">
            <CheckCircle className="h-12 w-12 text-gray-300 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-1">
              {filterStatus !== 'all' ? 'No matching tasks' : 'No compliance tasks'}
            </h3>
            <p className="text-center">
              {filterStatus !== 'all'
                ? 'Try adjusting your filter criteria'
                : 'Compliance tasks will appear here when created'
              }
            </p>
          </div>
        ) : (
          // Tasks list
          <div className="p-4 space-y-3">
            {filteredTasks.map((task) => {
              const status = getTaskStatus(task);
              const isCompleted = status === 'completed';
              const isOverdue = status === 'overdue';

              return (
                <div
                  key={task.id}
                  className={`bg-white rounded-lg p-4 border transition-colors ${
                    isOverdue ? 'border-red-200 bg-red-50' : 'border-gray-200'
                  }`}
                >
                  <div className="flex items-start space-x-3">
                    {/* Checkbox */}
                    <div className="pt-1">
                      <Checkbox
                        checked={isCompleted}
                        onCheckedChange={() => !isCompleted && handleMarkCompleted(task)}
                        disabled={isProcessing || isCompleted}
                        className="h-5 w-5"
                      />
                    </div>

                    {/* Content */}
                    <div className="flex-1 min-w-0">
                      {/* Title */}
                      <h3 className={`font-medium text-gray-900 mb-1 ${isCompleted ? 'line-through text-gray-500' : ''}`}>
                        {task.title}
                      </h3>

                      {/* Description */}
                      {task.description && (
                        <p className="text-sm text-gray-600 mb-2 line-clamp-2">
                          {task.description}
                        </p>
                      )}

                      {/* Badges and Due Date */}
                      <div className="flex flex-wrap items-center gap-2 text-sm">
                        {getStatusBadge(status)}
                        {getCycleBadge(task.cycle)}

                        {task.has_proof && (
                          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                            <FileText className="h-3 w-3 mr-1" />
                            Proof
                          </Badge>
                        )}

                        <span className="text-gray-500 flex items-center">
                          <Calendar className="h-3 w-3 mr-1" />
                          Due: {format(parseISO(task.next_due_date), 'MMM d, yyyy')}
                        </span>
                      </div>
                    </div>

                    {/* Proof Upload Button */}
                    <div className="pt-1">
                      <Button
                        variant={task.has_proof ? "default" : "outline"}
                        size="sm"
                        onClick={() => {
                          setSelectedTask(task);
                          fetchAttachments(task.id);
                          setUploadDialogOpen(true);
                        }}
                        className={`${task.has_proof ? "bg-blue-500 hover:bg-blue-600" : ""} h-8 w-8 p-0`}
                      >
                        <Upload className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* Refresh Button */}
      <div className="p-4 bg-white border-t">
        <Button
          onClick={fetchComplianceTasks}
          disabled={loading}
          className="w-full"
          variant="outline"
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Upload Proof Dialog */}
      <Dialog open={uploadDialogOpen} onOpenChange={setUploadDialogOpen}>
        <DialogContent className="sm:max-w-[400px] mx-4">
          <DialogHeader>
            <DialogTitle>Upload Proof</DialogTitle>
            <DialogDescription>
              Upload documents or photos as proof of completion.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            {selectedTask && (
              <div className="bg-blue-50 p-3 rounded-md">
                <p className="font-medium text-sm">{selectedTask.title}</p>
                <p className="text-xs text-gray-600">
                  Due: {format(parseISO(selectedTask.next_due_date), 'MMM d, yyyy')}
                </p>
              </div>
            )}

            <div className="space-y-2">
              <label htmlFor="file-upload" className="text-sm font-medium">
                Select File
              </label>
              <Input
                id="file-upload"
                type="file"
                accept=".pdf,.doc,.docx,.jpg,.jpeg,.png"
                onChange={(e) => {
                  if (e.target.files && e.target.files[0]) {
                    setSelectedFile(e.target.files[0]);
                  }
                }}
                className="text-sm"
              />
              <p className="text-xs text-gray-500">
                PDF, DOC, DOCX, JPG, PNG files accepted
              </p>
            </div>

            {/* Show existing attachments */}
            {attachments.length > 0 && (
              <div className="space-y-2">
                <h4 className="text-sm font-medium">Existing Proof</h4>
                <div className="border rounded-md divide-y max-h-32 overflow-y-auto">
                  {attachments.map((attachment) => (
                    <div key={attachment.id} className="p-2 flex justify-between items-center">
                      <div className="flex items-center min-w-0">
                        <FileText className="h-4 w-4 mr-2 text-blue-500 flex-shrink-0" />
                        <span className="text-sm truncate">{attachment.file_name}</span>
                      </div>
                      <div className="text-xs text-gray-500 ml-2 flex-shrink-0">
                        {format(parseISO(attachment.uploaded_at), 'MMM d')}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
          <DialogFooter className="flex-col sm:flex-row gap-2">
            <Button
              variant="outline"
              onClick={() => {
                setUploadDialogOpen(false);
                setSelectedFile(null);
              }}
              className="w-full sm:w-auto"
            >
              Cancel
            </Button>
            <Button
              onClick={handleFileUpload}
              disabled={isProcessing || !selectedFile}
              className="w-full sm:w-auto"
            >
              {isProcessing ? (
                <>
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  Uploading...
                </>
              ) : (
                <>
                  <Upload className="mr-2 h-4 w-4" />
                  Upload
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default PWAComplianceList;
