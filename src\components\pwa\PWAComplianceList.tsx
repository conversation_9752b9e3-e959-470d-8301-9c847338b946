import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Search,
  Plus,
  Calendar,
  CheckCircle,
  AlertCircle,
  Clock,
  FileText,
  RefreshCw,
  Filter
} from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import { format, parseISO, isAfter, isBefore, addDays, addWeeks, addMonths, addYears } from 'date-fns';

// Define types
interface ComplianceTask {
  id: string;
  title: string;
  description: string;
  cycle: 'daily' | 'weekly' | 'monthly' | 'annually';
  last_completed_at: string | null;
  next_due_date: string;
  created_at: string;
  created_by: string;
  has_proof?: boolean;
  parent_task_id?: string | null;
  completion_id?: string | null;
  is_completed?: boolean;
}

const PWAComplianceList: React.FC = () => {
  const { organizationId, user } = useAuth();
  const [complianceTasks, setComplianceTasks] = useState<ComplianceTask[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<'all' | 'pending' | 'completed' | 'overdue'>('all');
  const [isProcessing, setIsProcessing] = useState(false);

  // Calculate next due date based on cycle
  const calculateNextDueDate = (cycle: string): Date => {
    const now = new Date();
    switch (cycle) {
      case 'daily':
        return addDays(now, 1);
      case 'weekly':
        return addWeeks(now, 1);
      case 'monthly':
        return addMonths(now, 1);
      case 'annually':
        return addYears(now, 1);
      default:
        return addMonths(now, 1);
    }
  };

  // Fetch compliance tasks
  const fetchComplianceTasks = async () => {
    if (!organizationId) return;

    try {
      setLoading(true);

      // Fetch all compliance tasks
      const { data, error } = await supabase
        .from('compliance_tasks')
        .select('*')
        .eq('organization_id', organizationId)
        .order('next_due_date', { ascending: true });

      if (error) throw error;

      if (data) {
        const tasksWithProof = [];

        for (const task of data) {
          // Check for attachments (proof)
          const { data: attachments } = await supabase
            .from('compliance_attachments')
            .select('id')
            .eq('compliance_task_id', task.id);

          // Check for completion records
          const { data: completions } = await supabase
            .from('compliance_completions')
            .select('id')
            .eq('compliance_task_id', task.id)
            .order('completed_at', { ascending: false })
            .limit(1);

          // Add the task with proof and completion status
          tasksWithProof.push({
            ...task,
            has_proof: attachments && attachments.length > 0,
            is_completed: task.is_completed === true ? true :
              (task.last_completed_at ? isAfter(parseISO(task.last_completed_at), parseISO(task.next_due_date)) : false),
            completion_id: completions && completions.length > 0 ? completions[0].id : null
          });
        }

        setComplianceTasks(tasksWithProof);
      }
    } catch (error: any) {
      console.error('Error fetching compliance tasks:', error.message);
      toast({
        title: 'Error fetching compliance tasks',
        description: error.message,
        variant: 'destructive',
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle marking a task as completed
  const handleMarkCompleted = async (task: ComplianceTask) => {
    try {
      setIsProcessing(true);

      const now = new Date().toISOString();
      const nextDueDate = calculateNextDueDate(task.cycle).toISOString();

      // Add completion record
      const { data: completionData, error: completionError } = await supabase
        .from('compliance_completions')
        .insert({
          compliance_task_id: task.id,
          completed_by: user?.id,
          completed_at: now,
        })
        .select()
        .single();

      if (completionError) throw completionError;

      // Update current task as completed
      const { error: updateError } = await supabase
        .from('compliance_tasks')
        .update({
          last_completed_at: now,
          is_completed: true,
          updated_at: now,
        })
        .eq('id', task.id);

      if (updateError) throw updateError;

      // Create new task for next cycle
      const { error: newTaskError } = await supabase
        .from('compliance_tasks')
        .insert({
          organization_id: task.organization_id,
          title: task.title,
          description: task.description,
          cycle: task.cycle,
          next_due_date: nextDueDate,
          created_by: user?.id,
          parent_task_id: task.id,
          created_at: now,
          updated_at: now,
        });

      if (newTaskError) throw newTaskError;

      toast({
        title: 'Success',
        description: 'Task completed and next cycle created',
      });

      // Refresh tasks
      fetchComplianceTasks();
    } catch (error: any) {
      console.error('Error completing task:', error.message);
      toast({
        title: 'Error completing task',
        description: error.message,
        variant: 'destructive',
      });
    } finally {
      setIsProcessing(false);
    }
  };

  // Get task status
  const getTaskStatus = (task: ComplianceTask) => {
    if (task.is_completed) return 'completed';
    
    const now = new Date();
    const dueDate = parseISO(task.next_due_date);
    
    if (isBefore(dueDate, now)) return 'overdue';
    return 'pending';
  };

  // Get status badge
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge className="bg-green-100 text-green-800 border-green-200">Completed</Badge>;
      case 'overdue':
        return <Badge className="bg-red-100 text-red-800 border-red-200">Overdue</Badge>;
      default:
        return <Badge className="bg-yellow-100 text-yellow-800 border-yellow-200">Pending</Badge>;
    }
  };

  // Get cycle badge
  const getCycleBadge = (cycle: string) => {
    const colors = {
      daily: 'bg-blue-100 text-blue-800 border-blue-200',
      weekly: 'bg-purple-100 text-purple-800 border-purple-200',
      monthly: 'bg-orange-100 text-orange-800 border-orange-200',
      annually: 'bg-gray-100 text-gray-800 border-gray-200'
    };
    
    return (
      <Badge className={colors[cycle as keyof typeof colors] || colors.monthly}>
        {cycle.charAt(0).toUpperCase() + cycle.slice(1)}
      </Badge>
    );
  };

  // Filter tasks
  const filteredTasks = complianceTasks.filter(task => {
    const matchesSearch = task.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         task.description.toLowerCase().includes(searchTerm.toLowerCase());
    
    if (!matchesSearch) return false;
    
    const status = getTaskStatus(task);
    if (filterStatus === 'all') return true;
    return status === filterStatus;
  });

  // Initial fetch
  useEffect(() => {
    if (organizationId) {
      fetchComplianceTasks();
    }
  }, [organizationId]);

  return (
    <div className="flex flex-col h-full bg-gray-50">
      {/* Search and Filter Header */}
      <div className="bg-white border-b p-4 space-y-3">
        {/* Search */}
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <input
            type="text"
            placeholder="Search compliance tasks..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        {/* Filter Buttons */}
        <div className="flex gap-2 overflow-x-auto">
          {['all', 'pending', 'overdue', 'completed'].map((status) => (
            <Button
              key={status}
              variant={filterStatus === status ? 'default' : 'outline'}
              size="sm"
              onClick={() => setFilterStatus(status as any)}
              className="whitespace-nowrap"
            >
              {status.charAt(0).toUpperCase() + status.slice(1)}
            </Button>
          ))}
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto">
        {loading ? (
          // Loading state
          <div className="p-4 space-y-3">
            {Array(5).fill(0).map((_, i) => (
              <div key={i} className="bg-white rounded-lg p-4 border">
                <div className="flex items-start space-x-3">
                  <Skeleton className="h-5 w-5 rounded" />
                  <div className="flex-1 space-y-2">
                    <Skeleton className="h-5 w-3/4" />
                    <div className="flex gap-2">
                      <Skeleton className="h-6 w-16" />
                      <Skeleton className="h-6 w-20" />
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : filteredTasks.length === 0 ? (
          // Empty state
          <div className="flex flex-col items-center justify-center h-full text-gray-500 p-8">
            <CheckCircle className="h-12 w-12 text-gray-300 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-1">
              {searchTerm || filterStatus !== 'all' ? 'No matching tasks' : 'No compliance tasks'}
            </h3>
            <p className="text-center">
              {searchTerm || filterStatus !== 'all' 
                ? 'Try adjusting your search or filter criteria'
                : 'Compliance tasks will appear here when created'
              }
            </p>
          </div>
        ) : (
          // Tasks list
          <div className="p-4 space-y-3">
            {filteredTasks.map((task) => {
              const status = getTaskStatus(task);
              const isCompleted = status === 'completed';
              const isOverdue = status === 'overdue';

              return (
                <div
                  key={task.id}
                  className={`bg-white rounded-lg p-4 border transition-colors ${
                    isOverdue ? 'border-red-200 bg-red-50' : 'border-gray-200'
                  }`}
                >
                  <div className="flex items-start space-x-3">
                    {/* Checkbox */}
                    <div className="pt-1">
                      <Checkbox
                        checked={isCompleted}
                        onCheckedChange={() => !isCompleted && handleMarkCompleted(task)}
                        disabled={isProcessing || isCompleted}
                        className="h-5 w-5"
                      />
                    </div>

                    {/* Content */}
                    <div className="flex-1 min-w-0">
                      {/* Title */}
                      <h3 className={`font-medium text-gray-900 mb-1 ${isCompleted ? 'line-through text-gray-500' : ''}`}>
                        {task.title}
                      </h3>

                      {/* Description */}
                      {task.description && (
                        <p className="text-sm text-gray-600 mb-2 line-clamp-2">
                          {task.description}
                        </p>
                      )}

                      {/* Badges and Due Date */}
                      <div className="flex flex-wrap items-center gap-2 text-sm">
                        {getStatusBadge(status)}
                        {getCycleBadge(task.cycle)}
                        
                        {task.has_proof && (
                          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                            <FileText className="h-3 w-3 mr-1" />
                            Proof
                          </Badge>
                        )}

                        <span className="text-gray-500 flex items-center">
                          <Calendar className="h-3 w-3 mr-1" />
                          Due: {format(parseISO(task.next_due_date), 'MMM d, yyyy')}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* Refresh Button */}
      <div className="p-4 bg-white border-t">
        <Button
          onClick={fetchComplianceTasks}
          disabled={loading}
          className="w-full"
          variant="outline"
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>
    </div>
  );
};

export default PWAComplianceList;
