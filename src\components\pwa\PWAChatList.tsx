import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import {
  MessageSquare,
  RefreshCw,
  AlertCircle,
  WifiOff,
  Clock
} from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';
import { isOnline, isPWA, registerConnectivityListeners } from '@/utils/pwa-utils';
import PWAMobileLayout from './PWAMobileLayout';
import { useGetStreamChat } from '@/hooks/use-getstream-chat';
import { StreamChat } from 'stream-chat';
import { ensureTaskChatExists } from '@/utils/chatMembershipUtils';

const PWAChatList: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [chatThreads, setChatThreads] = useState<any[]>([]);
  const [streamClient, setStreamClient] = useState<StreamChat | null>(null);
  const [offlineMode, setOfflineMode] = useState(!isOnline());
  const [debugMode, setDebugMode] = useState(false);
  const [debugInfo, setDebugInfo] = useState<any>(null);

  // Effect to handle online/offline status
  useEffect(() => {
    const cleanup = registerConnectivityListeners(
      // Online callback
      () => {
        setOfflineMode(false);
        fetchChatThreads(); // Refresh data when coming back online
      },
      // Offline callback
      () => {
        setOfflineMode(true);
      }
    );

    return cleanup;
  }, []);

  // Initialize GetStream client
  useEffect(() => {
    const initializeStreamClient = async () => {
      if (!user) return;

      try {
        // Import the client initialization function
        const {
          getStreamClient,
          connectUser,
          syncUserChannelsWithTasks,
          getAllUserTaskChannels
        } = await import('@/integrations/getstream/client');

        // Get the client
        const client = getStreamClient();

        // Connect the user
        const userName = user.email?.split('@')[0] || 'User';
        await connectUser(user.id, userName, user.id);

        setStreamClient(client);
      } catch (error) {
        console.error('[PWAChatList] Error initializing Stream client:', error);
        setError('Failed to initialize chat. Please try again later.');
      }
    };

    initializeStreamClient();
  }, [user]);

  // Force create channels for all tasks the user is involved with
  const forceCreateAllTaskChannels = async () => {
    if (!user || !streamClient) return [];

    try {
      console.log('[PWAChatList] Force creating all task channels for user:', user.id);

      // Get ALL tasks the user is involved with, regardless of migration status
      const { data: userTasks, error: tasksError } = await supabase
        .from('tasks')
        .select('id, title, user_id, assigned_to, getstream_channel_id, chat_migrated_to_stream, status')
        .or(`user_id.eq.${user.id},assigned_to.eq.${user.id}`);

      if (tasksError) {
        console.error('[PWAChatList] Error fetching user tasks:', tasksError);
        return [];
      }

      console.log('[PWAChatList] Found tasks for user:', userTasks?.length || 0);

      if (debugMode) {
        setDebugInfo({
          allTasks: userTasks,
          userId: user.id
        });
      }

      if (!userTasks || userTasks.length === 0) {
        console.log('[PWAChatList] No tasks found for user');
        return [];
      }

      // Create channels for ALL tasks, not just migrated ones
      const createdChannels = [];

      for (const task of userTasks) {
        try {
          // Use centralized chat membership logic
          await ensureTaskChatExists(task.id, task.title);

          // Define channel ID
          const channelId = task.getstream_channel_id || `task-${task.id}`;

          // Get the channel (it should exist now)
          const channel = streamClient.channel('messaging', channelId);

          // Try to create the channel (this will fail silently if it already exists)
          try {
            await channel.create();
            console.log(`[PWAChatList] Created channel for task: ${task.id}`);
          } catch (createError) {
            // Channel might already exist, just log and continue
            console.log(`[PWAChatList] Channel may already exist for task: ${task.id}`);
          }

          // Make sure the user is a member
          try {
            await channel.addMembers([user.id]);
            console.log(`[PWAChatList] Added user to channel: ${channelId}`);
          } catch (memberError) {
            console.error(`[PWAChatList] Error adding user to channel: ${channelId}`, memberError);
          }

          // Watch the channel
          try {
            await channel.watch();
            console.log(`[PWAChatList] Watching channel: ${channelId}`);

            // Add to our list of created channels
            createdChannels.push({
              channel,
              channelId,
              taskId: task.id,
              taskTitle: task.title,
              status: task.status
            });

            // Update the task to mark it as migrated if needed
            if (!task.chat_migrated_to_stream) {
              await supabase
                .from('tasks')
                .update({
                  chat_migrated_to_stream: true,
                  getstream_channel_id: channelId
                })
                .eq('id', task.id);

              console.log(`[PWAChatList] Updated task ${task.id} to mark as migrated`);
            }
          } catch (watchError) {
            console.error(`[PWAChatList] Error watching channel: ${channelId}`, watchError);
          }
        } catch (taskError) {
          console.error(`[PWAChatList] Error processing task ${task.id}:`, taskError);
        }
      }

      console.log(`[PWAChatList] Successfully created/verified ${createdChannels.length} channels`);
      return createdChannels;
    } catch (error) {
      console.error('[PWAChatList] Error force creating task channels:', error);
      return [];
    }
  };

  // Fetch chat threads
  const fetchChatThreads = async () => {
    if (!user) return;

    setLoading(true);
    setError(null);

    try {
      // First try to get channels from GetStream
      if (streamClient) {
        try {
          // First, sync all user channels with tasks
          console.log('[PWAChatList] Syncing user channels with tasks');
          await syncUserChannelsWithTasks(user.id);

          // Get all task channels for the user
          console.log('[PWAChatList] Getting all task channels for user');
          const taskChannels = await getAllUserTaskChannels(user.id);
          console.log(`[PWAChatList] Found ${taskChannels.length} task channels`);

          if (debugMode) {
            setDebugInfo(prev => ({
              ...prev,
              taskChannels: taskChannels.map(c => ({
                id: c.id,
                name: c.data?.name,
                taskId: c.data?.task_id
              }))
            }));
          }

          // Also get channels from GetStream directly
          const filter = { type: 'messaging', members: { $in: [user.id] } };
          const sort = { last_message_at: -1 };

          const streamChannels = await streamClient.queryChannels(filter, sort, {
            limit: 50,
            state: true,
            watch: true,
          });

          console.log('[PWAChatList] GetStream channels found:', streamChannels.length);

          // SECURITY: Filter channels based on organization membership (OPTIMIZED)
          const { data: userProfile } = await supabase
            .from('profiles')
            .select('organization_id, account_type')
            .eq('id', user.id)
            .single();

          const secureStreamChannels = [];

          // Separate task and non-task channels
          const taskChannels = streamChannels.filter(channel => channel.data?.task_id);
          const nonTaskChannels = streamChannels.filter(channel => !channel.data?.task_id);

          // Add non-task channels immediately
          secureStreamChannels.push(...nonTaskChannels);

          if (taskChannels.length > 0) {
            // Batch query all tasks at once
            const taskIds = taskChannels.map(channel => channel.data.task_id);
            const { data: tasks, error: tasksError } = await supabase
              .from('tasks')
              .select('id, organization_id, visibility')
              .in('id', taskIds);

            if (tasksError) {
              console.error('[PWAChatList] Error fetching tasks for security filtering:', tasksError);
            } else if (tasks) {
              const taskMap = new Map(tasks.map(task => [task.id, task]));

              for (const channel of taskChannels) {
                const taskId = channel.data.task_id;
                const task = taskMap.get(taskId);

                if (!task) {
                  console.warn('[PWAChatList] Task not found for channel:', channel.id);
                  continue;
                }

                const hasAccess = (
                  userProfile?.organization_id === task.organization_id ||
                  (userProfile?.account_type === 'supplier' && task.visibility === 'public')
                );

                if (hasAccess) {
                  secureStreamChannels.push(channel);
                } else {
                  console.warn('[PWAChatList] Filtering out unauthorized channel:', {
                    channelId: channel.id,
                    taskId,
                    userOrg: userProfile?.organization_id,
                    taskOrg: task.organization_id
                  });
                }
              }
            }
          }

          console.log('[PWAChatList] Secure channels after filtering:', secureStreamChannels.length);

          if (debugMode) {
            setDebugInfo(prev => ({
              ...prev,
              streamChannels: secureStreamChannels.map(c => ({
                id: c.id,
                name: c.data?.name,
                members: c.state.members,
                taskId: c.data?.task_id
              }))
            }));
          }

          // Use only the secure channels (already filtered)
          const allChannels = secureStreamChannels;

          console.log(`[PWAChatList] Combined ${allChannels.length} unique channels`);

          if (debugMode) {
            setDebugInfo(prev => ({
              ...prev,
              combinedChannels: allChannels.map(c => ({
                id: c.id,
                name: c.data?.name,
                taskId: c.data?.task_id
              }))
            }));
          }

          // Format the channels into chat threads
          if (allChannels.length > 0) {
            const formattedThreads = await Promise.all(allChannels.map(async channel => {
              // Extract task ID from channel ID (format: task-{taskId})
              const taskId = channel.id.startsWith('task-') ? channel.id.substring(5) : channel.data?.task_id || null;

              // Get task title if we have a task ID and the channel doesn't have a name
              let channelName = channel.data?.name || 'Chat';
              if (taskId && (!channelName || channelName === 'Chat')) {
                try {
                  const { data: taskData } = await supabase
                    .from('tasks')
                    .select('title')
                    .eq('id', taskId)
                    .single();

                  if (taskData?.title) {
                    channelName = taskData.title;

                    // Update the channel name in GetStream
                    await channel.update({
                      name: taskData.title,
                      task_id: taskId
                    });
                  }
                } catch (error) {
                  console.error('[PWAChatList] Error fetching task title:', error);
                }
              }

              // Get the last message
              const lastMessage = channel.state.messages[channel.state.messages.length - 1];
              const lastMessageText = lastMessage ?
                (lastMessage.text || 'Attachment') :
                'No messages yet';

              return {
                id: channel.id,
                name: channelName,
                last_message: lastMessageText,
                updated_at: channel.data?.last_message_at || channel.created_at || new Date().toISOString(),
                task_id: taskId
              };
            }));

            console.log('[PWAChatList] Formatted chat threads:', formattedThreads.length);

            // Sort by updated_at
            formattedThreads.sort((a, b) => {
              return new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime();
            });

            setChatThreads(formattedThreads);
            setLoading(false);
            return;
          }
        } catch (streamError) {
          console.error('[PWAChatList] Error fetching GetStream channels:', streamError);
        }
      }

      // No fallback to Supabase - we're using GetStream exclusively
      console.log('[PWAChatList] No GetStream channels found or error occurred');
      setChatThreads([]);
    } catch (error: any) {
      console.error('[PWAChatList] Error fetching chat threads:', error);
      setError(error.message || 'Failed to load chat threads');
    } finally {
      setLoading(false);
    }
  };

  // Initial data fetch
  useEffect(() => {
    if (user) {
      fetchChatThreads();
    }
  }, [user, streamClient]);

  // Format date
  const formatDate = (dateString: string): string => {
    if (!dateString) return '';

    const date = new Date(dateString);
    const now = new Date();
    const diffDays = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60 * 24));

    if (diffDays === 0) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffDays === 1) {
      return 'Yesterday';
    } else if (diffDays < 7) {
      return date.toLocaleDateString([], { weekday: 'short' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  // Get initials for avatar
  const getInitials = (name: string): string => {
    if (!name) return 'C';
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  // Handle thread click
  const handleThreadClick = (thread: any) => {
    navigate(`/messages/${thread.id}${thread.task_id ? `?task=${thread.task_id}` : ''}`);
  };

  return (
    <PWAMobileLayout>
      <div className="container max-w-md mx-auto px-4 py-6">
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-xl font-semibold">Messages</h1>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setDebugMode(!debugMode)}
            >
              {debugMode ? 'Hide Debug' : 'Debug'}
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={fetchChatThreads}
              disabled={loading}
            >
              <RefreshCw className={`h-4 w-4 mr-1 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
        </div>

        {/* Offline indicator */}
        {offlineMode && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-md p-3 mb-4 flex items-center">
            <WifiOff className="h-5 w-5 text-yellow-500 mr-2" />
            <div className="text-sm text-yellow-700">
              You're offline. Chat functionality is limited.
            </div>
          </div>
        )}

        {/* Debug info */}
        {debugMode && debugInfo && (
          <div className="bg-gray-50 border border-gray-200 rounded-md p-3 mb-4 overflow-auto max-h-96">
            <h3 className="font-medium mb-2">Debug Info</h3>
            <div className="text-xs font-mono whitespace-pre-wrap">
              {JSON.stringify(debugInfo, null, 2)}
            </div>
          </div>
        )}

        {/* Error state */}
        {error && (
          <Card className="mb-6">
            <CardContent className="p-6 text-center">
              <AlertCircle className="h-8 w-8 text-red-500 mx-auto mb-2" />
              <p className="text-red-500">{error}</p>
              <Button variant="outline" className="mt-3" onClick={fetchChatThreads}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Try Again
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Loading state */}
        {loading && (
          <div className="rounded-lg overflow-hidden bg-white">
            {[...Array(4)].map((_, i) => (
              <React.Fragment key={i}>
                <div className="px-4 py-3">
                  <div className="flex items-center">
                    <Skeleton className="h-12 w-12 rounded-full mr-3 flex-shrink-0" />
                    <div className="flex-1">
                      <div className="flex justify-between items-baseline">
                        <Skeleton className="h-5 w-32" />
                        <Skeleton className="h-4 w-16 ml-2" />
                      </div>
                      <Skeleton className="h-4 w-48 mt-1" />
                    </div>
                  </div>
                </div>
                {i < 3 && (
                  <div className="h-px bg-gray-100 mx-4" />
                )}
              </React.Fragment>
            ))}
          </div>
        )}

        {/* Chat threads */}
        {!loading && chatThreads.length === 0 ? (
          <div className="text-center py-12">
            <MessageSquare className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-1">No messages yet</h3>
            <p className="text-gray-500">
              Your messages will appear here when you start chatting with others.
            </p>
          </div>
        ) : (
          <div className="rounded-lg overflow-hidden bg-white">
            {chatThreads.map((thread, index) => (
              <React.Fragment key={thread.id}>
                <div
                  className="px-4 py-3 cursor-pointer hover:bg-gray-50 transition-colors"
                  onClick={() => handleThreadClick(thread)}
                >
                  <div className="flex items-center">
                    <Avatar className="h-12 w-12 mr-3 flex-shrink-0">
                      <AvatarFallback>{getInitials(thread.name)}</AvatarFallback>
                    </Avatar>
                    <div className="flex-1 min-w-0">
                      <div className="flex justify-between items-baseline">
                        <h3 className="font-medium text-gray-900 truncate">{thread.name}</h3>
                        <span className="text-xs text-gray-400 ml-2 flex-shrink-0">
                          {formatDate(thread.updated_at)}
                        </span>
                      </div>
                      <p className="text-sm text-gray-500 truncate mt-1">{thread.last_message}</p>
                    </div>
                  </div>
                </div>
                {index < chatThreads.length - 1 && (
                  <div className="h-px bg-gray-100 mx-4" />
                )}
              </React.Fragment>
            ))}
          </div>
        )}
      </div>
    </PWAMobileLayout>
  );
};

export default PWAChatList;
